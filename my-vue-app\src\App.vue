<template>
  <div class="min-h-screen bg-white">
    <!-- Header with Logo -->
    <div class="pt-20 pb-8 text-center">
      <h1 class="text-6xl font-light text-gray-800 mb-2">
        Search<span class="text-blue-500">Hub</span>
      </h1>
      <p class="text-sm text-gray-500">Find anything, anywhere</p>
    </div>
    <!-- Search Container -->
    <div class="px-6 max-w-2xl mx-auto">
      <!-- Search Bar -->
      <div class="relative mb-4">
        <div class="relative">
          <i
            class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg"
          ></i>
          <input
            type="text"
            v-model="searchQuery"
            @input="handleInput"
            @keydown="handleKeydown"
            @focus="showSuggestions = true"
            @blur="hideSuggestions"
            placeholder="Search for anything..."
            class="w-full h-12 pl-12 pr-4 border border-gray-200 rounded-button text-base focus:outline-none focus:border-blue-400 focus:shadow-lg transition-all duration-200"
          />
        </div>
        <!-- Autocomplete Suggestions -->
        <div
          v-if="showSuggestions && filteredSuggestions.length > 0"
          class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-10 max-h-60 overflow-y-auto"
        >
          <div
            v-for="(suggestion, index) in filteredSuggestions.slice(0, 5)"
            :key="index"
            :class="{ 'bg-gray-50': selectedSuggestionIndex === index }"
            @mousedown="selectSuggestion(suggestion)"
            @mouseenter="selectedSuggestionIndex = index"
            class="px-4 py-3 cursor-pointer hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
          >
            <div class="flex items-center">
              <i class="fas fa-search text-gray-400 mr-3 text-sm"></i>
              <span class="text-gray-800">{{ suggestion }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- Tab Hint -->
      <div v-if="showTabHint" class="text-center mb-6">
        <p class="text-xs text-gray-500 flex items-center justify-center">
          <i class="fas fa-arrow-right mr-2"></i>
          Press "Tab" to confirm autocompletion
        </p>
      </div>
      <!-- Search Results -->
      <div v-if="searchResults.length > 0" class="mt-8">
        <div class="mb-4">
          <p class="text-sm text-gray-500">
            About {{ searchResults.length }} results (0.{{
            Math.floor(Math.random() * 9) + 1 }}{{ Math.floor(Math.random() * 9)
            }} seconds)
          </p>
        </div>
        <div class="space-y-6">
          <div
            v-for="(result, index) in searchResults"
            :key="index"
            class="cursor-pointer"
          >
            <div class="mb-1">
              <p class="text-sm text-green-600">{{ result.url }}</p>
            </div>
            <h3 class="text-xl text-blue-600 hover:underline mb-2">
              {{ result.title }}
            </h3>
            <p class="text-sm text-gray-600 leading-relaxed">
              {{ result.description }}
            </p>
          </div>
        </div>
      </div>
      <!-- No Results -->
      <div
        v-if="hasSearched && searchResults.length === 0 && searchQuery.length > 0"
        class="mt-8 text-center"
      >
        <i class="fas fa-search text-gray-300 text-4xl mb-4"></i>
        <p class="text-gray-500">No results found for "{{ searchQuery }}"</p>
        <p class="text-sm text-gray-400 mt-2">
          Try different keywords or check your spelling
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchQuery: "",
      showSuggestions: false,
      selectedSuggestionIndex: -1,
      hasSearched: false,
      showTabHint: false,
      suggestions: [
        "artificial intelligence",
        "machine learning tutorials",
        "web development best practices",
        "javascript frameworks 2025",
        "python programming guide",
        "data science projects",
        "mobile app development",
        "cloud computing services",
        "cybersecurity trends",
        "blockchain technology",
        "digital marketing strategies",
        "user experience design",
        "software engineering",
        "database management",
        "api development",
      ],
      mockResults: [
        {
          title: "Comprehensive Guide to Modern Web Development",
          url: "https://webdev-guide.com/modern-practices",
          description:
            "Learn the latest web development techniques, frameworks, and best practices. This comprehensive guide covers everything from HTML5 and CSS3 to advanced JavaScript frameworks like React, Vue, and Angular.",
        },
        {
          title: "JavaScript Frameworks Comparison 2025",
          url: "https://js-frameworks.dev/comparison-2025",
          description:
            "An in-depth comparison of the most popular JavaScript frameworks in 2025. Discover which framework is best suited for your next project with performance benchmarks and real-world examples.",
        },
        {
          title: "Machine Learning for Beginners - Complete Tutorial",
          url: "https://ml-tutorials.org/beginners-guide",
          description:
            "Start your machine learning journey with this beginner-friendly tutorial. Learn fundamental concepts, algorithms, and practical applications with hands-on examples and code samples.",
        },
        {
          title: "Python Programming Best Practices",
          url: "https://python-best-practices.com/guide",
          description:
            "Master Python programming with industry-standard best practices. This guide covers code organization, testing, documentation, and performance optimization techniques used by professional developers.",
        },
        {
          title: "Data Science Project Ideas and Implementation",
          url: "https://datasci-projects.net/ideas",
          description:
            "Explore creative data science project ideas with step-by-step implementation guides. Perfect for building your portfolio and demonstrating your analytical skills to potential employers.",
        },
        {
          title: "Cloud Computing Services Comparison",
          url: "https://cloud-services-review.com/comparison",
          description:
            "Compare leading cloud computing platforms including AWS, Google Cloud, and Microsoft Azure. Find the right cloud solution for your business needs with detailed feature analysis.",
        },
      ],
    };
  },
  computed: {
    filteredSuggestions() {
      if (!this.searchQuery) return [];
      return this.suggestions.filter((suggestion) =>
        suggestion.toLowerCase().includes(this.searchQuery.toLowerCase()),
      );
    },
    searchResults() {
      if (!this.searchQuery || this.searchQuery.length < 2) return [];
      return this.mockResults.filter(
        (result) =>
          result.title.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          result.description
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase()),
      );
    },
  },
  methods: {
    handleInput() {
      this.hasSearched = true;
      this.selectedSuggestionIndex = -1;
      this.showTabHint = this.filteredSuggestions.length > 0;
    },
    handleKeydown(event) {
      if (!this.showSuggestions || this.filteredSuggestions.length === 0)
        return;
      if (event.key === "ArrowDown") {
        event.preventDefault();
        this.selectedSuggestionIndex = Math.min(
          this.selectedSuggestionIndex + 1,
          this.filteredSuggestions.length - 1,
        );
      } else if (event.key === "ArrowUp") {
        event.preventDefault();
        this.selectedSuggestionIndex = Math.max(
          this.selectedSuggestionIndex - 1,
          -1,
        );
      } else if (event.key === "Tab" && this.selectedSuggestionIndex >= 0) {
        event.preventDefault();
        this.selectSuggestion(
          this.filteredSuggestions[this.selectedSuggestionIndex],
        );
      } else if (event.key === "Tab" && this.filteredSuggestions.length > 0) {
        event.preventDefault();
        this.selectSuggestion(this.filteredSuggestions[0]);
      } else if (event.key === "Enter") {
        if (this.selectedSuggestionIndex >= 0) {
          this.selectSuggestion(
            this.filteredSuggestions[this.selectedSuggestionIndex],
          );
        }
        this.showSuggestions = false;
      } else if (event.key === "Escape") {
        this.showSuggestions = false;
        this.selectedSuggestionIndex = -1;
      }
    },
    selectSuggestion(suggestion) {
      this.searchQuery = suggestion;
      this.showSuggestions = false;
      this.showTabHint = false;
      this.selectedSuggestionIndex = -1;
      this.hasSearched = true;
    },
    hideSuggestions() {
      setTimeout(() => {
        this.showSuggestions = false;
        this.showTabHint = false;
      }, 150);
    },
  },
};
</script>

<style scoped>
.rounded-button {
  border-radius: 24px !important;
}
input:focus {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
